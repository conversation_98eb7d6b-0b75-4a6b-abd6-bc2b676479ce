# Payment Configuration Guide

## Two Ways to Configure Payment Providers

### Option 1: Through Admin UI (Recommended for End Users)
1. Run the app and login as admin
2. Go to Admin Dashboard → Payment Configuration
3. Enter your payment provider credentials in the UI
4. Credentials are securely stored and encrypted

### Option 2: Through Code (For Developers)

#### Step 1: Add Your Credentials
Edit `lib/services/payment_config_service.dart` in the `setupProductionEnvironment()` method:

```dart
static Future<void> setupProductionEnvironment() async {
  // LemonSqueezy Configuration
  await configureLemonSqueezy(
    apiKey: 'YOUR_ACTUAL_API_KEY_HERE',
    storeId: 'YOUR_STORE_ID',
    webhookSecret: 'YOUR_WEBHOOK_SECRET',
    basicPlanId: 'YOUR_BASIC_PLAN_ID',
    premiumPlanId: 'YOUR_PREMIUM_PLAN_ID',
    isTestMode: false, // false for production, true for testing
  );
}
```

#### Step 2: Enable in main.dart
In `lib/main.dart`, uncomment this line:
```dart
await PaymentConfigService.setupProductionEnvironment();
```

#### Step 3: Your LemonSqueezy Credentials
Replace these placeholders with your actual values:

- **API Key**: Get from LemonSqueezy Dashboard → Settings → API
- **Store ID**: Your LemonSqueezy store ID (e.g., "202786")
- **Webhook Secret**: Create webhook in LemonSqueezy Dashboard
- **Plan IDs**: Your product variant IDs from LemonSqueezy

#### Example with Real Values:
```dart
await configureLemonSqueezy(
  apiKey: 'eyJ0eXAioiOkiJKV1QiJhbGciOiJSUzI1NiJ9...',
  storeId: '28726',
  webhookSecret: '211f7253476d9dc820a66447548f94d214de307e4ed264cbd23637f76fd5d09d9',
  basicPlanId: '583484',
  premiumPlanId: '582350',
  isTestMode: false,
);
```

## Quick Setup Methods

### Single Provider Setup

#### LemonSqueezy Only
```dart
await PaymentConfigService.setupLemonSqueezyOnly(
  apiKey: 'your_api_key',
  storeId: 'your_store_id',
  webhookSecret: 'your_webhook_secret',
  basicPlanId: 'your_basic_plan_id',
  premiumPlanId: 'your_premium_plan_id',
);
```

#### Stripe Only
```dart
await PaymentConfigService.setupStripeOnly(
  secretKey: 'sk_live_...',
  webhookSecret: 'whsec_...',
  basicPriceId: 'price_...',
  premiumPriceId: 'price_...',
);
```

#### PayPal Only
```dart
await PaymentConfigService.setupPayPalOnly(
  clientId: 'your_client_id',
  clientSecret: 'your_client_secret',
  webhookId: 'your_webhook_id',
  basicPlanId: 'your_basic_plan_id',
  premiumPlanId: 'your_premium_plan_id',
);
```

### Multiple Providers Setup
```dart
final results = await PaymentConfigService.setupMultipleProviders(
  // LemonSqueezy
  lemonApiKey: 'your_lemon_api_key',
  lemonStoreId: 'your_store_id',
  lemonWebhookSecret: 'your_webhook_secret',
  lemonBasicPlanId: 'basic_plan_id',
  lemonPremiumPlanId: 'premium_plan_id',

  // Stripe (optional)
  stripeSecretKey: 'sk_live_...',
  stripeWebhookSecret: 'whsec_...',
  stripeBasicPriceId: 'price_...',
  stripePremiumPriceId: 'price_...',

  // PayPal (optional)
  paypalClientId: 'your_client_id',
  paypalClientSecret: 'your_client_secret',
  paypalWebhookId: 'your_webhook_id',
  paypalBasicPlanId: 'basic_plan_id',
  paypalPremiumPlanId: 'premium_plan_id',

  isTestMode: false,
);

// Results will show which providers were successfully configured
print(results); // {'lemonsqueezy': true, 'stripe': true, 'paypal': false}
```

### Individual Provider Setup
```dart
// LemonSqueezy
await PaymentConfigService.configureLemonSqueezy(
  apiKey: 'your_key',
  storeId: 'your_store',
  webhookSecret: 'your_secret',
  basicPlanId: 'basic_id',
  premiumPlanId: 'premium_id',
);

// Stripe
await PaymentConfigService.configureStripe(
  secretKey: 'sk_live_...',
  webhookSecret: 'whsec_...',
  basicPriceId: 'price_...',
  premiumPriceId: 'price_...',
);

// PayPal
await PaymentConfigService.configurePayPal(
  clientId: 'your_client_id',
  clientSecret: 'your_client_secret',
  webhookId: 'your_webhook_id',
  basicPlanId: 'your_basic_plan',
  premiumPlanId: 'your_premium_plan',
);
```

## Where to Call These Methods

### In main.dart (App Startup)
```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  
  // Configure payment providers
  await PaymentConfigService.setupProductionEnvironment();
  
  // Or load from storage (admin UI configs)
  await PaymentConfigService.initializeFromStorage();
  
  runApp(const MyApp());
}
```

### In a Specific Screen
```dart
class MyScreen extends StatefulWidget {
  @override
  void initState() {
    super.initState();
    _setupPayments();
  }
  
  void _setupPayments() async {
    await PaymentConfigService.setupLemonSqueezyOnly(
      apiKey: 'your_api_key',
      storeId: 'your_store_id',
      webhookSecret: 'your_webhook_secret',
      basicPlanId: 'your_basic_plan_id',
      premiumPlanId: 'your_premium_plan_id',
    );
  }
}
```

## Security Notes
- Never commit real API keys to version control
- Use environment variables or secure storage for production
- The admin UI automatically encrypts and stores credentials securely
- Code configuration is immediate but less secure for distribution

## Testing vs Production
- Set `isTestMode: true` for testing with sandbox credentials
- Set `isTestMode: false` for production with live credentials
- Use test API keys during development
- Switch to live keys before app distribution
