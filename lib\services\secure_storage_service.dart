import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class SecureStorageService {
  static const String _emailKey = 'saved_email';
  static const String _rememberMeKey = 'remember_me';
  static const String _lastLoginKey = 'last_login_time';
  static const String _appSessionKey = 'app_session_state';
  static const String _lastAppCloseKey = 'last_app_close_time';

  static Future<void> saveCredentials({
    required String email,
    required String password,
    required bool rememberMe,
  }) async {
    final prefs = await SharedPreferences.getInstance();

    if (rememberMe) {
      await prefs.setString(_emailKey, email.trim().toLowerCase());

      await prefs.setBool(_rememberMeKey, true);

      await prefs.setString(_lastLoginKey, DateTime.now().toIso8601String());
    } else {
      await clearCredentials();
    }
  }

  static Future<String?> getSavedEmail() async {
    final prefs = await SharedPreferences.getInstance();
    final rememberMe = prefs.getBool(_rememberMeKey) ?? false;
    
    if (rememberMe) {
      return prefs.getString(_emailKey);
    }
    return null;
  }



  static Future<bool> isRememberMeEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_rememberMeKey) ?? false;
  }

  static Future<DateTime?> getLastLoginTime() async {
    final prefs = await SharedPreferences.getInstance();
    final timeString = prefs.getString(_lastLoginKey);
    
    if (timeString != null) {
      return DateTime.tryParse(timeString);
    }
    return null;
  }

  static Future<void> clearCredentials() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_emailKey);
    await prefs.remove(_rememberMeKey);
    await prefs.remove(_lastLoginKey);
  }

  static Future<bool> areCredentialsExpired() async {
    final lastLogin = await getLastLoginTime();
    if (lastLogin == null) return true;
    
    final now = DateTime.now();
    final difference = now.difference(lastLogin);
    
    return difference.inDays > 30;
  }

  static Future<SavedCredentials?> getSavedCredentials() async {
    final rememberMe = await isRememberMeEnabled();
    if (!rememberMe) return null;
    
    final expired = await areCredentialsExpired();
    if (expired) {
      await clearCredentials();
      return null;
    }
    
    final email = await getSavedEmail();
    if (email == null) return null;
    
    return SavedCredentials(
      email: email,
    );
  }

  static Future<void> updateLastLoginTime() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastLoginKey, DateTime.now().toIso8601String());
  }


  static Future<void> setAppSessionState(AppSessionState state) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_appSessionKey, state.name);
  }

  static Future<AppSessionState> getAppSessionState() async {
    final prefs = await SharedPreferences.getInstance();
    final stateString = prefs.getString(_appSessionKey);

    if (stateString == null) {
      return AppSessionState.freshStart;
    }

    return AppSessionState.values.firstWhere(
      (state) => state.name == stateString,
      orElse: () => AppSessionState.freshStart,
    );
  }

  static Future<void> markAppClosed() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastAppCloseKey, DateTime.now().toIso8601String());
  }

  static Future<bool> wasAppRecentlyClosed() async {
    final prefs = await SharedPreferences.getInstance();
    final closeTimeString = prefs.getString(_lastAppCloseKey);

    if (closeTimeString == null) return false;

    final closeTime = DateTime.tryParse(closeTimeString);
    if (closeTime == null) return false;

    final now = DateTime.now();
    final difference = now.difference(closeTime);

    return difference.inSeconds <= 10;
  }

  static Future<void> clearSessionState() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_appSessionKey);
    await prefs.remove(_lastAppCloseKey);
  }

  static Future<void> forceFreshStart() async {
    await clearSessionState();
    await setAppSessionState(AppSessionState.freshStart);
  }


  static Future<void> storeEncrypted(String key, String value) async {
    final prefs = await SharedPreferences.getInstance();
    final encrypted = _simpleEncrypt(value);
    await prefs.setString('encrypted_$key', encrypted);
  }

  static Future<String?> getDecrypted(String key) async {
    final prefs = await SharedPreferences.getInstance();
    final encrypted = prefs.getString('encrypted_$key');
    if (encrypted == null) return null;

    try {
      return _simpleDecrypt(encrypted);
    } catch (e) {
      print('Failed to decrypt $key: $e');
      return null;
    }
  }

  static String _simpleEncrypt(String value) {
    final bytes = utf8.encode(value);
    return base64.encode(bytes);
  }

  static String _simpleDecrypt(String encrypted) {
    final bytes = base64.decode(encrypted);
    return utf8.decode(bytes);
  }

  static Future<bool> isAPIConfigured() async {
    final openai = await getDecrypted('openai_api_key');
    final elevenlabs = await getDecrypted('elevenlabs_api_key');
    return openai != null && elevenlabs != null;
  }

  static Future<bool> isLemonSqueezyConfigured() async {
    final storeId = await getDecrypted('lemonsqueezy_store_id');
    final apiKey = await getDecrypted('lemonsqueezy_api_key');
    return storeId != null && apiKey != null;
  }
}

class SavedCredentials {
  final String email;

  SavedCredentials({
    required this.email,
  });
}

enum AppSessionState {
  freshStart,
  logoutLogin,
  appRestart,
}
