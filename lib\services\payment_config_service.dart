import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../models/payment_models.dart';
import '../models/subscription_model.dart';
import 'payment_manager.dart';

class PaymentConfigService {
  static Future<bool> configureLemonSqueezy({
    required String apiKey,
    required String storeId,
    required String webhookSecret,
    required String basicPlanId,
    required String premiumPlanId,
    bool isTestMode = false,
  }) async {
    try {
      final config = PaymentConfig(
        provider: PaymentProvider.lemonsqueezy,
        apiKey: apiKey,
        storeId: storeId,
        webhookSecret: webhookSecret,
        isTestMode: isTestMode,
        planIds: {
          SubscriptionPlan.basic: basicPlanId,
          SubscriptionPlan.premium: premiumPlanId,
        },
      );

      await PaymentManager.instance.addProvider(config);
      debugPrint('LemonSqueezy configured successfully');
      return true;
    } catch (e) {
      debugPrint('Failed to configure LemonSqueezy: $e');
      return false;
    }
  }

  static Future<bool> configureStripe({
    required String secretKey,
    required String webhookSecret,
    required String basicPriceId,
    required String premiumPriceId,
    bool isTestMode = false,
  }) async {
    try {
      final config = PaymentConfig(
        provider: PaymentProvider.stripe,
        apiKey: secretKey,
        webhookSecret: webhookSecret,
        isTestMode: isTestMode,
        planIds: {
          SubscriptionPlan.basic: basicPriceId,
          SubscriptionPlan.premium: premiumPriceId,
        },
      );

      await PaymentManager.instance.addProvider(config);
      debugPrint('Stripe configured successfully');
      return true;
    } catch (e) {
      debugPrint('Failed to configure Stripe: $e');
      return false;
    }
  }

  static Future<bool> configurePayPal({
    required String clientId,
    required String clientSecret,
    required String webhookId,
    required String basicPlanId,
    required String premiumPlanId,
    bool isTestMode = false,
  }) async {
    try {
      final config = PaymentConfig(
        provider: PaymentProvider.paypal,
        apiKey: clientId,
        secretKey: clientSecret,
        webhookSecret: webhookId,
        isTestMode: isTestMode,
        planIds: {
          SubscriptionPlan.basic: basicPlanId,
          SubscriptionPlan.premium: premiumPlanId,
        },
      );

      await PaymentManager.instance.addProvider(config);
      debugPrint('PayPal configured successfully');
      return true;
    } catch (e) {
      debugPrint('Failed to configure PayPal: $e');
      return false;
    }
  }

  /// Configure payment providers using environment variables
  /// Call this method in your app initialization to set up payment providers from .env
  static Future<void> setupProductionEnvironment() async {
    // LemonSqueezy Configuration from environment variables
    final lemonApiKey = dotenv.env['LEMONSQUEEZY_API_KEY'];
    final lemonStoreId = dotenv.env['LEMONSQUEEZY_STORE_ID'];
    final lemonWebhookSecret = dotenv.env['LEMONSQUEEZY_WEBHOOK_SECRET'];
    final lemonBasicPlanId = dotenv.env['LEMONSQUEEZY_BASIC_PLAN_ID'];
    final lemonPremiumPlanId = dotenv.env['LEMONSQUEEZY_PREMIUM_PLAN_ID'];

    if (lemonApiKey != null && lemonStoreId != null && lemonWebhookSecret != null &&
        lemonBasicPlanId != null && lemonPremiumPlanId != null) {
      await configureLemonSqueezy(
        apiKey: lemonApiKey,
        storeId: lemonStoreId,
        webhookSecret: lemonWebhookSecret,
        basicPlanId: lemonBasicPlanId,
        premiumPlanId: lemonPremiumPlanId,
        isTestMode: dotenv.env['PAYMENT_TEST_MODE']?.toLowerCase() == 'true',
      );
      debugPrint('LemonSqueezy configured from environment variables');
    } else {
      debugPrint('LemonSqueezy environment variables not found, skipping configuration');
    }

    // Stripe Configuration from environment variables
    final stripeSecretKey = dotenv.env['STRIPE_SECRET_KEY'];
    final stripeWebhookSecret = dotenv.env['STRIPE_WEBHOOK_SECRET'];
    final stripeBasicPriceId = dotenv.env['STRIPE_BASIC_PRICE_ID'];
    final stripePremiumPriceId = dotenv.env['STRIPE_PREMIUM_PRICE_ID'];

    if (stripeSecretKey != null && stripeWebhookSecret != null &&
        stripeBasicPriceId != null && stripePremiumPriceId != null) {
      await configureStripe(
        secretKey: stripeSecretKey,
        webhookSecret: stripeWebhookSecret,
        basicPriceId: stripeBasicPriceId,
        premiumPriceId: stripePremiumPriceId,
        isTestMode: dotenv.env['PAYMENT_TEST_MODE']?.toLowerCase() == 'true',
      );
      debugPrint('Stripe configured from environment variables');
    } else {
      debugPrint('Stripe environment variables not found, skipping configuration');
    }

    // PayPal Configuration from environment variables
    final paypalClientId = dotenv.env['PAYPAL_CLIENT_ID'];
    final paypalClientSecret = dotenv.env['PAYPAL_CLIENT_SECRET'];
    final paypalWebhookId = dotenv.env['PAYPAL_WEBHOOK_ID'];
    final paypalBasicPlanId = dotenv.env['PAYPAL_BASIC_PLAN_ID'];
    final paypalPremiumPlanId = dotenv.env['PAYPAL_PREMIUM_PLAN_ID'];

    if (paypalClientId != null && paypalClientSecret != null && paypalWebhookId != null &&
        paypalBasicPlanId != null && paypalPremiumPlanId != null) {
      await configurePayPal(
        clientId: paypalClientId,
        clientSecret: paypalClientSecret,
        webhookId: paypalWebhookId,
        basicPlanId: paypalBasicPlanId,
        premiumPlanId: paypalPremiumPlanId,
        isTestMode: dotenv.env['PAYMENT_TEST_MODE']?.toLowerCase() == 'true',
      );
      debugPrint('PayPal configured from environment variables');
    } else {
      debugPrint('PayPal environment variables not found, skipping configuration');
    }

    debugPrint('Production payment environment configured from .env file');
  }

  /// Quick setup for LemonSqueezy only (most common use case)
  static Future<bool> setupLemonSqueezyOnly({
    required String apiKey,
    required String storeId,
    required String webhookSecret,
    required String basicPlanId,
    required String premiumPlanId,
    bool isTestMode = false,
  }) async {
    return await configureLemonSqueezy(
      apiKey: apiKey,
      storeId: storeId,
      webhookSecret: webhookSecret,
      basicPlanId: basicPlanId,
      premiumPlanId: premiumPlanId,
      isTestMode: isTestMode,
    );
  }

  /// Quick setup for Stripe only
  static Future<bool> setupStripeOnly({
    required String secretKey,
    required String webhookSecret,
    required String basicPriceId,
    required String premiumPriceId,
    bool isTestMode = false,
  }) async {
    return await configureStripe(
      secretKey: secretKey,
      webhookSecret: webhookSecret,
      basicPriceId: basicPriceId,
      premiumPriceId: premiumPriceId,
      isTestMode: isTestMode,
    );
  }

  /// Quick setup for PayPal only
  static Future<bool> setupPayPalOnly({
    required String clientId,
    required String clientSecret,
    required String webhookId,
    required String basicPlanId,
    required String premiumPlanId,
    bool isTestMode = false,
  }) async {
    return await configurePayPal(
      clientId: clientId,
      clientSecret: clientSecret,
      webhookId: webhookId,
      basicPlanId: basicPlanId,
      premiumPlanId: premiumPlanId,
      isTestMode: isTestMode,
    );
  }

  /// Setup multiple providers at once (advanced use case)
  static Future<Map<String, bool>> setupMultipleProviders({
    // LemonSqueezy
    String? lemonApiKey,
    String? lemonStoreId,
    String? lemonWebhookSecret,
    String? lemonBasicPlanId,
    String? lemonPremiumPlanId,
    // Stripe
    String? stripeSecretKey,
    String? stripeWebhookSecret,
    String? stripeBasicPriceId,
    String? stripePremiumPriceId,
    // PayPal
    String? paypalClientId,
    String? paypalClientSecret,
    String? paypalWebhookId,
    String? paypalBasicPlanId,
    String? paypalPremiumPlanId,
    // Global settings
    bool isTestMode = false,
  }) async {
    final results = <String, bool>{};

    // Configure LemonSqueezy if credentials provided
    if (lemonApiKey != null && lemonStoreId != null && lemonWebhookSecret != null &&
        lemonBasicPlanId != null && lemonPremiumPlanId != null) {
      results['lemonsqueezy'] = await setupLemonSqueezyOnly(
        apiKey: lemonApiKey,
        storeId: lemonStoreId,
        webhookSecret: lemonWebhookSecret,
        basicPlanId: lemonBasicPlanId,
        premiumPlanId: lemonPremiumPlanId,
        isTestMode: isTestMode,
      );
    }

    // Configure Stripe if credentials provided
    if (stripeSecretKey != null && stripeWebhookSecret != null &&
        stripeBasicPriceId != null && stripePremiumPriceId != null) {
      results['stripe'] = await setupStripeOnly(
        secretKey: stripeSecretKey,
        webhookSecret: stripeWebhookSecret,
        basicPriceId: stripeBasicPriceId,
        premiumPriceId: stripePremiumPriceId,
        isTestMode: isTestMode,
      );
    }

    // Configure PayPal if credentials provided
    if (paypalClientId != null && paypalClientSecret != null && paypalWebhookId != null &&
        paypalBasicPlanId != null && paypalPremiumPlanId != null) {
      results['paypal'] = await setupPayPalOnly(
        clientId: paypalClientId,
        clientSecret: paypalClientSecret,
        webhookId: paypalWebhookId,
        basicPlanId: paypalBasicPlanId,
        premiumPlanId: paypalPremiumPlanId,
        isTestMode: isTestMode,
      );
    }

    debugPrint('Multiple providers setup completed: $results');
    return results;
  }

  static bool validateLemonSqueezyConfig({
    required String apiKey,
    required String storeId,
    required String webhookSecret,
    required String basicPlanId,
    required String premiumPlanId,
  }) {
    return apiKey.isNotEmpty &&
           storeId.isNotEmpty &&
           webhookSecret.isNotEmpty &&
           basicPlanId.isNotEmpty &&
           premiumPlanId.isNotEmpty;
  }

  static bool validateStripeConfig({
    required String secretKey,
    required String webhookSecret,
    required String basicPriceId,
    required String premiumPriceId,
  }) {
    return secretKey.startsWith('sk_') &&
           webhookSecret.startsWith('whsec_') &&
           basicPriceId.startsWith('price_') &&
           premiumPriceId.startsWith('price_');
  }

  static bool validatePayPalConfig({
    required String clientId,
    required String clientSecret,
    required String webhookId,
    required String basicPlanId,
    required String premiumPlanId,
  }) {
    return clientId.isNotEmpty &&
           clientSecret.isNotEmpty &&
           webhookId.isNotEmpty &&
           basicPlanId.isNotEmpty &&
           premiumPlanId.isNotEmpty;
  }

  static Map<String, dynamic> getConfigurationStatus() {
    final providerInfo = PaymentManager.instance.providerInfo;
    
    return {
      'lemonsqueezy': {
        'configured': providerInfo.containsKey(PaymentProvider.lemonsqueezy),
        'testMode': providerInfo[PaymentProvider.lemonsqueezy]?['isTestMode'] ?? false,
        'hasApiKey': providerInfo[PaymentProvider.lemonsqueezy]?['hasApiKey'] ?? false,
        'hasWebhookSecret': providerInfo[PaymentProvider.lemonsqueezy]?['hasWebhookSecret'] ?? false,
      },
      'stripe': {
        'configured': providerInfo.containsKey(PaymentProvider.stripe),
        'testMode': providerInfo[PaymentProvider.stripe]?['isTestMode'] ?? false,
        'hasApiKey': providerInfo[PaymentProvider.stripe]?['hasApiKey'] ?? false,
        'hasWebhookSecret': providerInfo[PaymentProvider.stripe]?['hasWebhookSecret'] ?? false,
      },
      'paypal': {
        'configured': providerInfo.containsKey(PaymentProvider.paypal),
        'testMode': providerInfo[PaymentProvider.paypal]?['isTestMode'] ?? false,
        'hasApiKey': providerInfo[PaymentProvider.paypal]?['hasApiKey'] ?? false,
        'hasWebhookSecret': providerInfo[PaymentProvider.paypal]?['hasWebhookSecret'] ?? false,
      },
    };
  }

  static Future<void> removeProvider(PaymentProvider provider) async {
    await PaymentManager.instance.removeProvider(provider);
    debugPrint('Removed payment provider: ${provider.name}');
  }

  static void setPrimaryProvider(PaymentProvider provider) {
    PaymentManager.instance.setPrimaryProvider(provider);
    debugPrint('Set primary payment provider: ${provider.name}');
  }

  /// Validates provider configuration without making external API calls
  static Future<bool> validateProviderConfiguration(PaymentProvider provider) async {
    try {
      final providerInstance = PaymentManager.instance.getProvider(provider);
      if (providerInstance == null) {
        debugPrint('Provider not configured: ${provider.name}');
        return false;
      }

      // Basic validation - check if provider has required configuration
      final config = PaymentManager.instance.getConfig(provider);
      if (config == null) {
        debugPrint('Provider configuration missing: ${provider.name}');
        return false;
      }

      // Validate required fields based on provider type
      switch (provider) {
        case PaymentProvider.lemonsqueezy:
          return validateLemonSqueezyConfig(
            apiKey: config.apiKey,
            storeId: config.storeId ?? '',
            webhookSecret: config.webhookSecret ?? '',
            basicPlanId: config.planIds[SubscriptionPlan.basic] ?? '',
            premiumPlanId: config.planIds[SubscriptionPlan.premium] ?? '',
          );
        case PaymentProvider.stripe:
          return validateStripeConfig(
            secretKey: config.apiKey,
            webhookSecret: config.webhookSecret ?? '',
            basicPriceId: config.planIds[SubscriptionPlan.basic] ?? '',
            premiumPriceId: config.planIds[SubscriptionPlan.premium] ?? '',
          );
        case PaymentProvider.paypal:
          return validatePayPalConfig(
            clientId: config.apiKey,
            clientSecret: config.secretKey ?? '',
            webhookId: config.webhookSecret ?? '',
            basicPlanId: config.planIds[SubscriptionPlan.basic] ?? '',
            premiumPlanId: config.planIds[SubscriptionPlan.premium] ?? '',
          );
      }
    } catch (e) {
      debugPrint('Provider configuration validation error: $e');
      return false;
    }
  }

  static String? getCheckoutUrl({
    required SubscriptionPlan plan,
    required String customerEmail,
    PaymentProvider? preferredProvider,
  }) {
    return PaymentManager.instance.getCheckoutUrl(
      plan: plan,
      customerEmail: customerEmail,
      preferredProvider: preferredProvider,
    );
  }

  static Future<void> initializeFromStorage() async {
    try {
      await PaymentManager.instance.loadFromStorage();
      debugPrint('Payment system initialized from storage');
    } catch (e) {
      debugPrint('Failed to initialize payment system from storage: $e');
    }
  }

  static List<PaymentProvider> getAvailableProviders() {
    return PaymentManager.instance.availableProviders;
  }

  static PaymentProvider? getPrimaryProvider() {
    return PaymentManager.instance.primaryProviderType;
  }

  static bool get isReady => PaymentManager.instance.isInitialized;

  static List<String> getProviderFeatures(PaymentProvider provider) {
    final providerInstance = PaymentManager.instance.getProvider(provider);
    return providerInstance?.supportedFeatures ?? [];
  }
}
